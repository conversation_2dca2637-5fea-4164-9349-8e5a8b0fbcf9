const { chromium } = require('playwright');

class OptimizedMexcTrader {
    constructor(port = 9223) {
        this.browser = null;
        this.page = null;
        this.port = port;
        this.isExecutingTrade = false;
        this.monitoringActive = false;
        this.monitoringInterval = null;
        this.lastBalance = null;
        this.balanceUpdateTime = null;
    }

    async connectToBrowser() {
        console.log(`🔗 Connecting to browser on port ${this.port}...`);
        
        try {
            this.browser = await chromium.connectOverCDP(`http://localhost:${this.port}`);
            const contexts = this.browser.contexts();
            
            if (contexts.length > 0) {
                const pages = contexts[0].pages();
                this.page = pages.length > 0 ? pages[0] : await contexts[0].newPage();
            } else {
                const context = await this.browser.newContext();
                this.page = await context.newPage();
            }
            
            console.log(`✅ Connected to browser on port ${this.port}`);
            return true;
        } catch (error) {
            console.error(`❌ Connection failed to port ${this.port}:`, error.message);
            return false;
        }
    }

    async startBackgroundMonitoring() {
        if (this.monitoringActive) {
            console.log('⚠️ Monitoring already active');
            return;
        }

        this.monitoringActive = true;
        console.log('🔄 Starting background monitoring...');

        // Run monitoring every 10 seconds for more responsive preparation
        this.monitoringInterval = setInterval(async () => {
            if (!this.isExecutingTrade && this.monitoringActive) {
                await this.performBackgroundMaintenance();
            }
        }, 10000);

        // Initial setup
        await this.performBackgroundMaintenance();
    }

    async performBackgroundMaintenance() {
        // CRITICAL: Never run during trade execution
        if (this.isExecutingTrade) {
            console.log('⚡ PERFORMANCE PROTECTION: Skipping maintenance during trade execution');
            return;
        }

        try {
            console.log('🧹 Background maintenance (between trades only)...');

            // 1. Ensure we're on Open panel (for next open trade)
            await this.ensureOpenPanel();

            // 2. Close any popups (prevent blocking future trades)
            await this.closeAnyPopups();

            // 3. Clear quantity field if it has value (clean slate for next trade)
            await this.clearQuantityField();

            // 4. Update balance (monitoring only)
            await this.updateBalance();

            // 5. Memory optimization (safe mode - no cookies clearing)
            await this.optimizeMemory();

            console.log('✅ Background maintenance completed - panel ready for next trade');
        } catch (error) {
            console.log('⚠️ Background maintenance error:', error.message);
        }
    }

    async ensureOpenPanel() {
        try {
            // Check both Open and Close tabs to determine current state
            const openTab = this.page.locator('span[data-testid="contract-trade-order-form-tab-open"]').first();
            const closeTab = this.page.locator('span[data-testid="contract-trade-order-form-tab-close"]').first();

            if (await openTab.isVisible({ timeout: 1000 })) {
                // Check if Open tab is active by looking for active class
                const openTabClass = await openTab.getAttribute('class');
                const closeTabClass = await closeTab.getAttribute('class');

                const isOpenActive = openTabClass && openTabClass.includes('handle_active');
                const isCloseActive = closeTabClass && closeTabClass.includes('handle_active');

                if (!isOpenActive || isCloseActive) {
                    console.log('🔄 Panel State Monitor: Switching to Open panel...');
                    await openTab.click();
                    await this.page.waitForTimeout(300);
                    console.log('✅ Panel State Monitor: Switched to Open panel');
                } else {
                    console.log('✅ Panel State Monitor: Already on Open panel');
                }
            }
        } catch (error) {
            console.log('⚠️ Panel State Monitor: Could not ensure Open panel -', error.message);
        }
    }

    async closeAnyPopups() {
        const popupSelectors = [
            // MEXC specific popup selectors
            'button:has-text("Confirm")',
            'button:has-text("Cancel")',
            'button:has-text("Close")',
            'button:has-text("OK")',
            'button:has-text("Got it")',
            // Generic modal close buttons
            '.modal-close',
            '.ant-modal-close',
            '[aria-label="Close"]',
            // MEXC specific close buttons
            '.close-btn',
            '.popup-close'
        ];

        let popupsFound = 0;
        for (const selector of popupSelectors) {
            try {
                const elements = await this.page.locator(selector).all();
                for (const element of elements) {
                    if (await element.isVisible({ timeout: 100 })) {
                        await element.click({ timeout: 200 });
                        popupsFound++;
                        console.log(`🗑️ Field Cleanup Monitor: Closed popup - ${selector}`);
                        await this.page.waitForTimeout(100); // Brief pause between popup closures
                    }
                }
            } catch (error) {
                // Continue to next selector
            }
        }

        if (popupsFound === 0) {
            console.log('✅ Field Cleanup Monitor: No popups found');
        } else {
            console.log(`✅ Field Cleanup Monitor: Closed ${popupsFound} popup(s)`);
        }
    }

    async clearQuantityField() {
        try {
            // Multiple selectors for quantity field to improve reliability
            const quantitySelectors = [
                'text=Quantity(USDT) >> xpath=following::input[1]',
                'input[placeholder*="Quantity"]',
                'input[placeholder*="USDT"]',
                '.quantity-input input',
                '[data-testid*="quantity"] input'
            ];

            let fieldCleared = false;
            for (const selector of quantitySelectors) {
                try {
                    const quantityInput = this.page.locator(selector).first();

                    if (await quantityInput.isVisible({ timeout: 300 })) {
                        const currentValue = await quantityInput.inputValue();
                        if (currentValue && currentValue.trim() !== '') {
                            await quantityInput.click();
                            await quantityInput.fill('');
                            console.log(`🧹 Field Cleanup Monitor: Cleared quantity field (${currentValue}) using selector: ${selector}`);
                            fieldCleared = true;
                            break;
                        }
                    }
                } catch (error) {
                    // Try next selector
                    continue;
                }
            }

            if (!fieldCleared) {
                console.log('✅ Field Cleanup Monitor: Quantity field is clean');
            }
        } catch (error) {
            console.log('⚠️ Field Cleanup Monitor: Could not access quantity field -', error.message);
        }
    }

    async updateBalance() {
        try {
            const balanceElement = this.page.locator('span.AssetsItem_num__9eLwJ').first();
            
            if (await balanceElement.isVisible({ timeout: 1000 })) {
                const balanceText = await balanceElement.textContent();
                const balanceMatch = balanceText.match(/([0-9]+\.?[0-9]*)\s*USDT/);
                
                if (balanceMatch) {
                    this.lastBalance = parseFloat(balanceMatch[1]);
                    this.balanceUpdateTime = new Date().toISOString();
                    console.log(`💰 Balance updated: ${this.lastBalance} USDT`);
                }
            }
        } catch (error) {
            console.log('⚠️ Could not update balance');
        }
    }

    async optimizeMemory() {
        try {
            // REMOVED: clearCookies() as it causes MEXC logout
            // Only do safe memory optimization

            // Force garbage collection if available
            if (global.gc) {
                global.gc();
            }

            console.log('🗑️ Memory optimized (safe mode - no cookie clearing)');
        } catch (error) {
            // Memory optimization is optional
        }
    }

    async executeOpenTrade(orderType, quantity = '0.3600') {
        const startTime = Date.now();
        this.isExecutingTrade = true;

        try {
            console.log(`🎯 EXECUTING ${orderType.toUpperCase()}...`);
            console.log(`⚡ PERFORMANCE MODE: No monitoring during execution`);

            // PERFORMANCE CRITICAL: No monitoring calls during execution
            // Panel should already be prepared by background monitoring

            // Step 1: Fill quantity (direct execution, no monitoring)
            console.log('🔢 Filling quantity...');
            const quantityInput = this.page.locator('text=Quantity(USDT) >> xpath=following::input[1]').first();
            await quantityInput.click();
            await quantityInput.fill(quantity);
            console.log(`✅ Quantity filled: ${quantity}`);

            // Step 2: Click appropriate button (direct execution, no monitoring)
            let buttonSelector;
            if (orderType === 'Open Long') {
                buttonSelector = 'button.component_longBtn__eazYU div:has-text("Open Long")';
            } else if (orderType === 'Open Short') {
                buttonSelector = 'button.component_shortBtn__x5P3I div:has-text("Open Short")';
            } else {
                throw new Error(`Invalid open order type: ${orderType}`);
            }

            console.log(`📊 Clicking ${orderType} button...`);
            const orderButton = this.page.locator(buttonSelector).first();
            await orderButton.click();
            console.log(`✅ ${orderType} clicked - Trade considered DONE`);

            const executionTime = Date.now() - startTime;
            console.log(`⚡ ${orderType} completed in ${executionTime}ms`);

            return {
                success: true,
                executionTime,
                orderType,
                quantity,
                targetAchieved: executionTime < 2000,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            const executionTime = Date.now() - startTime;
            console.error(`❌ ${orderType} failed: ${error.message}`);
            
            return {
                success: false,
                error: error.message,
                executionTime,
                orderType,
                quantity
            };
        } finally {
            this.isExecutingTrade = false;
        }
    }

    async executeCloseTrade(orderType, quantity = '0.3600') {
        const startTime = Date.now();
        this.isExecutingTrade = true;
        
        try {
            console.log(`🎯 EXECUTING ${orderType.toUpperCase()}...`);

            // Step 1: Switch to Close panel
            console.log('🔄 Switching to Close panel...');
            const closeTab = this.page.locator('span[data-testid="contract-trade-order-form-tab-close"]').first();
            await closeTab.click();
            await this.page.waitForTimeout(500);
            console.log('✅ Switched to Close panel');

            // Step 2: Fill quantity (same selector as open trades and monitoring)
            console.log('🔢 Filling quantity...');
            const quantityInput = this.page.locator('text=Quantity(USDT) >> xpath=following::input[1]').first();
            await quantityInput.click();
            await quantityInput.fill(quantity);
            console.log(`✅ Quantity filled: ${quantity}`);

            // Step 3: Click appropriate close button (multiple selector strategies)
            let buttonSelectors;
            if (orderType === 'Close Long') {
                buttonSelectors = [
                    // Primary: Exact selector from user
                    'button.ant-btn.ant-btn-default.component_longBtn__eazYU.component_withColor__LqLhs',
                    // Fallback: Class-based selector
                    'button.component_longBtn__eazYU',
                    // Fallback: Text-based selector
                    'button:has-text("Close Long")'
                ];
            } else if (orderType === 'Close Short') {
                buttonSelectors = [
                    // Primary: Exact selector from user
                    'button.ant-btn.ant-btn-default.component_shortBtn__x5P3I.component_withColor__LqLhs',
                    // Fallback: Class-based selector
                    'button.component_shortBtn__x5P3I',
                    // Fallback: Text-based selector
                    'button:has-text("Close Short")'
                ];
            } else {
                throw new Error(`Invalid close order type: ${orderType}`);
            }

            console.log(`📊 Clicking ${orderType} button...`);

            // Try selectors in order until one works
            let buttonClicked = false;
            for (const selector of buttonSelectors) {
                try {
                    const closeButton = this.page.locator(selector).first();
                    if (await closeButton.isVisible({ timeout: 1000 })) {
                        await closeButton.click();
                        console.log(`✅ ${orderType} clicked using selector: ${selector}`);
                        buttonClicked = true;
                        break;
                    }
                } catch (error) {
                    console.log(`⚠️ Selector failed: ${selector} - ${error.message}`);
                    continue;
                }
            }

            if (!buttonClicked) {
                throw new Error(`Could not find ${orderType} button with any selector`);
            }

            console.log(`✅ ${orderType} clicked - Trade considered CLOSED`);

            const executionTime = Date.now() - startTime;
            console.log(`⚡ ${orderType} completed in ${executionTime}ms`);

            // Step 4: Schedule immediate background maintenance after close trade
            this.schedulePostTradeCleanup();

            return {
                success: true,
                executionTime,
                orderType,
                quantity,
                targetAchieved: executionTime < 2000,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            const executionTime = Date.now() - startTime;
            console.error(`❌ ${orderType} failed: ${error.message}`);
            
            return {
                success: false,
                error: error.message,
                executionTime,
                orderType,
                quantity
            };
        } finally {
            this.isExecutingTrade = false;
        }
    }

    async executeOrder(orderType, quantity = '0.3600') {
        console.log('🚀 PERFORMANCE MODE: Direct execution with pre-prepared panel');

        // Pre-execution: Ensure panel is ready (only if not already executing)
        if (orderType.includes('Open')) {
            const ready = await this.ensurePreExecutionReadiness();
            if (!ready) {
                console.log('⚠️ Panel preparation failed, proceeding with execution anyway');
            }
        }

        // Execute trade with zero monitoring interference
        if (orderType.includes('Open')) {
            return await this.executeOpenTrade(orderType, quantity);
        } else if (orderType.includes('Close')) {
            return await this.executeCloseTrade(orderType, quantity);
        } else {
            throw new Error(`Unknown order type: ${orderType}`);
        }
    }

    getBalance() {
        return {
            balance: this.lastBalance,
            lastUpdate: this.balanceUpdateTime,
            currency: 'USDT'
        };
    }

    getStats() {
        return {
            isExecutingTrade: this.isExecutingTrade,
            monitoringActive: this.monitoringActive,
            lastBalance: this.lastBalance,
            balanceUpdateTime: this.balanceUpdateTime,
            port: this.port
        };
    }

    async ensurePreExecutionReadiness() {
        // CRITICAL: Only run when NOT executing trades
        if (this.isExecutingTrade) {
            console.log('⚡ PERFORMANCE PROTECTION: Cannot prepare during active execution');
            return false;
        }

        try {
            console.log('🚀 Pre-execution preparation...');

            // Quick check to ensure panel is ready (minimal operations only)
            const openTab = this.page.locator('span[data-testid="contract-trade-order-form-tab-open"]').first();
            if (await openTab.isVisible({ timeout: 500 })) {
                const openTabClass = await openTab.getAttribute('class');
                if (!openTabClass || !openTabClass.includes('handle_active')) {
                    console.log('🔄 Quick switch to Open panel before execution...');
                    await openTab.click();
                    await this.page.waitForTimeout(200); // Minimal wait
                }
            }

            console.log('✅ Pre-execution preparation complete');
            return true;
        } catch (error) {
            console.log('⚠️ Pre-execution preparation failed:', error.message);
            return false;
        }
    }

    async schedulePostTradeCleanup() {
        // Schedule immediate cleanup after close trades to prepare for next open trade
        setTimeout(async () => {
            if (!this.isExecutingTrade && this.monitoringActive) {
                console.log('🔄 Post-trade cleanup initiated...');
                await this.performBackgroundMaintenance();
            }
        }, 500); // Quick cleanup after trade completion
    }

    async stopMonitoring() {
        this.monitoringActive = false;
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        console.log('🛑 Background monitoring stopped');
    }
}

async function executeCommand(orderType, port) {
    const trader = new OptimizedMexcTrader(port);
    
    try {
        console.log('🎯 MEXC OPTIMIZED TRADER');
        console.log('=========================');
        console.log(`📊 Order: ${orderType}`);
        console.log(`🌐 Port: ${port}`);
        console.log('⚡ Target: <2 seconds');
        console.log('🔄 Background monitoring: Enabled');
        console.log('');

        const connected = await trader.connectToBrowser();
        if (!connected) {
            throw new Error(`Failed to connect to port ${port}`);
        }

        // Start background monitoring
        await trader.startBackgroundMonitoring();

        // Execute the trade
        const result = await trader.executeOrder(orderType);

        if (result.success) {
            if (result.targetAchieved) {
                console.log('\n🏆 TARGET ACHIEVED!');
                console.log(`⚡ ${result.executionTime}ms (<2 seconds)`);
            } else {
                console.log('\n✅ SUCCESS!');
                console.log(`⏱️ ${result.executionTime}ms`);
            }
        } else {
            console.log('\n❌ FAILED');
            if (result.error) console.log(`Error: ${result.error}`);
        }

        // Show balance
        const balance = trader.getBalance();
        if (balance.balance !== null) {
            console.log(`💰 Current Balance: ${balance.balance} USDT`);
        }

        require('fs').writeFileSync(`optimized-${orderType.replace(' ', '')}-${port}.json`, JSON.stringify(result, null, 2));
        process.exit(result.success ? 0 : 1);
        
    } catch (error) {
        console.error('💥 Failed:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    const orderType = process.argv[2];
    const port = parseInt(process.argv[3]) || 9223;
    
    const validOrders = ['Open Long', 'Open Short', 'Close Long', 'Close Short'];
    
    if (!orderType || !validOrders.includes(orderType)) {
        console.log('🎯 MEXC OPTIMIZED TRADER');
        console.log('=========================');
        console.log('📋 USAGE:');
        console.log('node optimized-mexc-trader.js "Open Long" [port]');
        console.log('node optimized-mexc-trader.js "Open Short" [port]');
        console.log('node optimized-mexc-trader.js "Close Long" [port]');
        console.log('node optimized-mexc-trader.js "Close Short" [port]');
        console.log('');
        console.log('🌐 Default Port: 9223');
        console.log('🔄 Features: Background monitoring, memory optimization, balance tracking');
        process.exit(1);
    }
    
    executeCommand(orderType, port);
}

module.exports = OptimizedMexcTrader;
