const EventEmitter = require('events');

class PositionManager extends EventEmitter {
    constructor(configManager, marketDataService, tradingExecutor, logger) {
        super();
        this.configManager = configManager;
        this.marketDataService = marketDataService;
        this.tradingExecutor = tradingExecutor;
        this.logger = logger;
        
        this.positions = new Map(); // Store active positions
        this.monitoringInterval = null;
        this.monitoringFrequency = 1000; // Check every 1 second
        this.isMonitoring = false;

        // Failure tracking to prevent infinite loops
        this.positionFailures = new Map(); // Track failures per position
        this.maxFailuresPerPosition = 3; // Maximum failures before abandoning position
        this.failureCooldown = 60000; // 1 minute cooldown after max failures
    }

    updateConfig(configManager, marketDataService, tradingExecutor) {
        this.configManager = configManager;
        this.marketDataService = marketDataService;
        this.tradingExecutor = tradingExecutor;
    }

    async addPosition(positionData) {
        const config = this.configManager.getConfig();
        
        if (!config.slTpEnabled) {
            this.logger.info('SL/TP disabled, not adding position for monitoring');
            return null;
        }

        try {
            // Get current ATR for SL/TP calculation
            const atr = await this.marketDataService.getATR(positionData.symbol);
            
            // Calculate SL/TP levels
            const slTpLevels = this.marketDataService.calculateSLTP(
                positionData.entryPrice,
                atr,
                config,
                positionData.direction || 'long'
            );

            const position = {
                id: positionData.id || `pos_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                symbol: positionData.symbol,
                direction: positionData.direction || 'long',
                entryPrice: positionData.entryPrice,
                quantity: positionData.quantity,
                originalQuantity: positionData.quantity,
                entryTime: new Date().toISOString(),
                
                // SL/TP Data
                atr: atr,
                stopLoss: slTpLevels.stopLoss,
                takeProfits: slTpLevels.takeProfits,
                trailingStart: slTpLevels.trailingStart,
                
                // Status
                status: 'active',
                isTrailing: false,
                trailingStopLoss: null,
                highestPrice: positionData.entryPrice, // For trailing
                lowestPrice: positionData.entryPrice, // For short trailing
                slType: slTpLevels.slType || 'Normal',
                moveToTPsActive: false,
                currentSLLevel: 'initial', // initial, tp1, tp2, tp3, entry
                
                // Execution tracking
                tpExecutions: [],
                lastPriceCheck: null,
                
                // Metadata
                tradeId: positionData.tradeId,
                requestId: positionData.requestId
            };

            this.positions.set(position.id, position);
            
            this.logger.info('Position added for monitoring', {
                positionId: position.id,
                symbol: position.symbol,
                entryPrice: position.entryPrice,
                stopLoss: position.stopLoss,
                takeProfits: position.takeProfits.length,
                atr: position.atr
            });

            // Start monitoring if not already running
            this.startMonitoring();
            
            this.emit('positionAdded', position);
            return position;
            
        } catch (error) {
            this.logger.error('Failed to add position for monitoring', {
                error: error.message,
                positionData
            });
            throw error;
        }
    }

    startMonitoring() {
        if (this.isMonitoring) return;
        
        this.isMonitoring = true;
        this.monitoringInterval = setInterval(() => {
            this.checkPositions();
        }, this.monitoringFrequency);
        
        this.logger.info('Position monitoring started');
    }

    stopMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        this.isMonitoring = false;
        this.logger.info('Position monitoring stopped');
    }

    async checkPositions() {
        if (this.positions.size === 0) {
            this.stopMonitoring();
            return;
        }

        for (const [positionId, position] of this.positions) {
            try {
                await this.checkPosition(position);
            } catch (error) {
                this.logger.error('Error checking position', {
                    positionId,
                    error: error.message
                });
            }
        }
    }

    async checkPosition(position) {
        try {
            // Get current market price
            const priceData = await this.marketDataService.getCurrentPrice(position.symbol);
            const currentPrice = priceData.price;
            
            position.lastPriceCheck = new Date().toISOString();
            
            // Update highest/lowest price for trailing
            if (position.direction === 'long' && currentPrice > position.highestPrice) {
                position.highestPrice = currentPrice;

                // Update trailing stop loss if trailing is active
                if (position.isTrailing) {
                    const config = this.configManager.getConfig();
                    const trailingDistance = position.atr * config.trailingValue;
                    position.trailingStopLoss = position.highestPrice - trailingDistance;
                }
            } else if (position.direction === 'short' && currentPrice < position.lowestPrice) {
                position.lowestPrice = currentPrice;

                // Update trailing stop loss for short if trailing is active
                if (position.isTrailing) {
                    const config = this.configManager.getConfig();
                    const trailingDistance = position.atr * config.trailingValue;
                    position.trailingStopLoss = position.lowestPrice + trailingDistance;
                }
            }

            // Handle Move to TPs logic
            if (position.slType === 'MoveToTPs') {
                await this.handleMoveToTPs(position, currentPrice);
            }

            // Check if trailing should start (FIXED FOR SHORT POSITIONS)
            const shouldStartTrailing = position.direction === 'long'
                ? (!position.isTrailing && position.trailingStart && currentPrice >= position.trailingStart)
                : (!position.isTrailing && position.trailingStart && currentPrice <= position.trailingStart);

            if (shouldStartTrailing) {
                position.isTrailing = true;
                const config = this.configManager.getConfig();
                const trailingDistance = position.atr * config.trailingValue;

                // CORRECT TRAILING SL CALCULATION:
                // LONG: SL below highest price (highestPrice - trailingDistance)
                // SHORT: SL above lowest price (lowestPrice + trailingDistance)
                position.trailingStopLoss = position.direction === 'long'
                    ? position.highestPrice - trailingDistance
                    : position.lowestPrice + trailingDistance;

                this.logger.info('Trailing stop loss activated', {
                    positionId: position.id,
                    direction: position.direction,
                    currentPrice,
                    trailingStart: position.trailingStart,
                    trailingStopLoss: position.trailingStopLoss,
                    referencePrice: position.direction === 'long' ? position.highestPrice : position.lowestPrice
                });
            }

            // Check Stop Loss (FIXED LOGIC)
            const stopLossPrice = position.isTrailing ? position.trailingStopLoss : position.stopLoss;

            // CORRECT SL LOGIC:
            // LONG positions: SL triggers when price goes DOWN (currentPrice <= stopLossPrice)
            // SHORT positions: SL triggers when price goes UP (currentPrice >= stopLossPrice)
            const shouldTriggerSL = position.direction === 'long'
                ? currentPrice <= stopLossPrice  // Long: price drops below SL
                : currentPrice >= stopLossPrice; // Short: price rises above SL

            // DEBUG: Log SL check details
            console.log(`🔍 SL Check - Direction: ${position.direction}, Current: ${currentPrice}, SL: ${stopLossPrice}, Should Trigger: ${shouldTriggerSL}`);

            if (shouldTriggerSL) {
                await this.executeStopLoss(position, currentPrice);
                return;
            }

            // Check Take Profits
            for (const tp of position.takeProfits) {
                if (!tp.enabled) continue;
                
                const alreadyExecuted = position.tpExecutions.find(exec => exec.level === tp.level);
                if (alreadyExecuted) continue;

                const shouldTriggerTP = position.direction === 'long'
                    ? currentPrice >= tp.price
                    : currentPrice <= tp.price;

                if (shouldTriggerTP) {
                    await this.executeTakeProfit(position, tp, currentPrice);
                }
            }
            
        } catch (error) {
            this.logger.error('Error in position check', {
                positionId: position.id,
                error: error.message
            });
        }
    }

    async executeStopLoss(position, currentPrice) {
        try {
            // Check if position has exceeded failure limit
            const failureKey = `${position.id}_sl`;
            const failures = this.positionFailures.get(failureKey) || { count: 0, lastFailure: null };

            if (failures.count >= this.maxFailuresPerPosition) {
                const timeSinceLastFailure = Date.now() - failures.lastFailure;
                if (timeSinceLastFailure < this.failureCooldown) {
                    this.logger.warn('Stop loss execution skipped due to failure limit', {
                        positionId: position.id,
                        failureCount: failures.count,
                        cooldownRemaining: this.failureCooldown - timeSinceLastFailure
                    });
                    return;
                } else {
                    // Reset failure count after cooldown
                    this.positionFailures.delete(failureKey);
                    this.logger.info('Stop loss failure count reset after cooldown', {
                        positionId: position.id
                    });
                }
            }

            this.logger.info('Executing stop loss', {
                positionId: position.id,
                currentPrice,
                stopLoss: position.isTrailing ? position.trailingStopLoss : position.stopLoss,
                quantity: position.quantity,
                failureCount: failures.count
            });

            // Execute close trade with correct direction
            const orderType = position.direction === 'long' ? 'Close Long' : 'Close Short';
            const closeResult = await this.tradingExecutor.executeTrade({
                orderType,
                quantity: position.quantity.toString(),
                reason: 'stop_loss',
                positionId: position.id
            });

            if (closeResult.success) {
                position.status = 'closed_sl';
                position.closePrice = currentPrice;
                position.closeTime = new Date().toISOString();
                position.closeReason = 'stop_loss';

                this.logger.info('Stop loss executed successfully', {
                    positionId: position.id,
                    closePrice: currentPrice,
                    pnl: this.calculatePnL(position, currentPrice)
                });

                // Clear any failure tracking for this position
                this.positionFailures.delete(failureKey);

                this.emit('stopLossExecuted', position, closeResult);
                this.positions.delete(position.id);
            } else {
                // Track failure
                failures.count++;
                failures.lastFailure = Date.now();
                this.positionFailures.set(failureKey, failures);

                this.logger.error('Stop loss execution failed', {
                    positionId: position.id,
                    error: closeResult.error,
                    failureCount: failures.count,
                    maxFailures: this.maxFailuresPerPosition
                });

                // If max failures reached, abandon position
                if (failures.count >= this.maxFailuresPerPosition) {
                    this.logger.error('Stop loss max failures reached, abandoning position', {
                        positionId: position.id,
                        failureCount: failures.count
                    });

                    position.status = 'abandoned_sl_failure';
                    position.closeTime = new Date().toISOString();
                    position.closeReason = 'abandoned_max_failures';

                    this.emit('positionAbandoned', position, 'stop_loss_max_failures');
                    this.positions.delete(position.id);
                }
            }

        } catch (error) {
            // Track failure for exceptions too
            const failureKey = `${position.id}_sl`;
            const failures = this.positionFailures.get(failureKey) || { count: 0, lastFailure: null };
            failures.count++;
            failures.lastFailure = Date.now();
            this.positionFailures.set(failureKey, failures);

            this.logger.error('Stop loss execution error', {
                positionId: position.id,
                error: error.message,
                failureCount: failures.count
            });

            // If max failures reached, abandon position
            if (failures.count >= this.maxFailuresPerPosition) {
                this.logger.error('Stop loss max failures reached due to exceptions, abandoning position', {
                    positionId: position.id,
                    failureCount: failures.count
                });

                position.status = 'abandoned_sl_error';
                position.closeTime = new Date().toISOString();
                position.closeReason = 'abandoned_max_errors';

                this.emit('positionAbandoned', position, 'stop_loss_max_errors');
                this.positions.delete(position.id);
            }
        }
    }

    async executeTakeProfit(position, tp, currentPrice) {
        try {
            // Check if this TP level has exceeded failure limit
            const failureKey = `${position.id}_tp_${tp.level}`;
            const failures = this.positionFailures.get(failureKey) || { count: 0, lastFailure: null };

            if (failures.count >= this.maxFailuresPerPosition) {
                const timeSinceLastFailure = Date.now() - failures.lastFailure;
                if (timeSinceLastFailure < this.failureCooldown) {
                    this.logger.warn('Take profit execution skipped due to failure limit', {
                        positionId: position.id,
                        level: tp.level,
                        failureCount: failures.count,
                        cooldownRemaining: this.failureCooldown - timeSinceLastFailure
                    });
                    return;
                } else {
                    // Reset failure count after cooldown
                    this.positionFailures.delete(failureKey);
                    this.logger.info('Take profit failure count reset after cooldown', {
                        positionId: position.id,
                        level: tp.level
                    });
                }
            }

            const closeQuantity = (position.quantity * tp.percent / 100);

            this.logger.info('Executing take profit', {
                positionId: position.id,
                level: tp.level,
                currentPrice,
                targetPrice: tp.price,
                closeQuantity,
                percent: tp.percent,
                failureCount: failures.count
            });

            // Execute partial close with correct direction
            const orderType = position.direction === 'long' ? 'Close Long' : 'Close Short';
            const closeResult = await this.tradingExecutor.executeTrade({
                orderType,
                quantity: closeQuantity.toString(),
                reason: `take_profit_${tp.level}`,
                positionId: position.id
            });

            if (closeResult.success) {
                // Clear any failure tracking for this TP level
                this.positionFailures.delete(failureKey);

                // Record TP execution
                position.tpExecutions.push({
                    level: tp.level,
                    price: currentPrice,
                    quantity: closeQuantity,
                    time: new Date().toISOString(),
                    result: closeResult
                });
                
                // Update remaining quantity
                position.quantity -= closeQuantity;
                
                this.logger.info('Take profit executed successfully', {
                    positionId: position.id,
                    level: tp.level,
                    closePrice: currentPrice,
                    remainingQuantity: position.quantity
                });
                
                this.emit('takeProfitExecuted', position, tp, closeResult);
                
                // Close position if no quantity remaining
                if (position.quantity <= 0.0001) { // Small threshold for floating point
                    position.status = 'closed_tp';
                    position.closeTime = new Date().toISOString();
                    position.closeReason = 'take_profit_complete';
                    this.positions.delete(position.id);
                }
            } else {
                // Track failure
                failures.count++;
                failures.lastFailure = Date.now();
                this.positionFailures.set(failureKey, failures);

                this.logger.error('Take profit execution failed', {
                    positionId: position.id,
                    level: tp.level,
                    error: closeResult.error,
                    failureCount: failures.count,
                    maxFailures: this.maxFailuresPerPosition
                });

                // If max failures reached, disable this TP level
                if (failures.count >= this.maxFailuresPerPosition) {
                    this.logger.error('Take profit max failures reached, disabling TP level', {
                        positionId: position.id,
                        level: tp.level,
                        failureCount: failures.count
                    });

                    tp.enabled = false;
                    tp.disabledReason = 'max_failures_reached';
                    tp.disabledTime = new Date().toISOString();
                }
            }

        } catch (error) {
            // Track failure for exceptions too
            const failureKey = `${position.id}_tp_${tp.level}`;
            const failures = this.positionFailures.get(failureKey) || { count: 0, lastFailure: null };
            failures.count++;
            failures.lastFailure = Date.now();
            this.positionFailures.set(failureKey, failures);

            this.logger.error('Take profit execution error', {
                positionId: position.id,
                level: tp.level,
                error: error.message,
                failureCount: failures.count
            });

            // If max failures reached, disable this TP level
            if (failures.count >= this.maxFailuresPerPosition) {
                this.logger.error('Take profit max failures reached due to exceptions, disabling TP level', {
                    positionId: position.id,
                    level: tp.level,
                    failureCount: failures.count
                });

                tp.enabled = false;
                tp.disabledReason = 'max_errors_reached';
                tp.disabledTime = new Date().toISOString();
            }
        }
    }

    async handleMoveToTPs(position, currentPrice) {
        const config = this.configManager.getConfig();

        // Sort TPs by level to check in order
        const sortedTPs = position.takeProfits.sort((a, b) => a.level - b.level);

        for (const tp of sortedTPs) {
            const alreadyExecuted = position.tpExecutions.find(exec => exec.level === tp.level);
            if (alreadyExecuted) continue;

            const tpReached = position.direction === 'long'
                ? currentPrice >= tp.price
                : currentPrice <= tp.price;

            if (tpReached) {
                // Move SL based on TP level
                let newSL;

                if (tp.level === 1) {
                    // At TP1, move SL to entry
                    newSL = position.entryPrice;
                    position.currentSLLevel = 'entry';
                } else if (tp.level === 2) {
                    // At TP2, move SL to TP1
                    const tp1 = position.takeProfits.find(t => t.level === 1);
                    if (tp1) {
                        newSL = tp1.price;
                        position.currentSLLevel = 'tp1';
                    }
                } else if (tp.level === 3) {
                    // At TP3, move SL to TP2
                    const tp2 = position.takeProfits.find(t => t.level === 2);
                    if (tp2) {
                        newSL = tp2.price;
                        position.currentSLLevel = 'tp2';
                    }
                }

                if (newSL !== undefined) {
                    position.stopLoss = newSL;
                    this.logger.info('Move to TPs: SL updated', {
                        positionId: position.id,
                        tpLevel: tp.level,
                        newSL,
                        currentSLLevel: position.currentSLLevel
                    });
                }

                break; // Only process the first reached TP
            }
        }
    }

    calculatePnL(position, currentPrice) {
        if (position.direction === 'long') {
            return (currentPrice - position.entryPrice) * position.originalQuantity;
        } else {
            return (position.entryPrice - currentPrice) * position.originalQuantity;
        }
    }

    getActivePositions() {
        return Array.from(this.positions.values());
    }

    getPosition(positionId) {
        return this.positions.get(positionId);
    }

    removePosition(positionId) {
        const removed = this.positions.delete(positionId);
        if (removed) {
            this.logger.info('Position removed from monitoring', { positionId });
            this.emit('positionRemoved', positionId);
        }
        return removed;
    }

    async forceCloseAllPositions(reason = 'force_close_for_new_trade') {
        const activePositions = this.getActivePositions();
        const closedPositions = [];
        const failedPositions = [];

        if (activePositions.length === 0) {
            this.logger.info('No active positions to force close');
            return { success: true, closedPositions: [], failedPositions: [] };
        }

        this.logger.info(`Force closing ${activePositions.length} active positions`, { reason });

        for (const position of activePositions) {
            try {
                this.logger.info('Force closing position', {
                    positionId: position.id,
                    symbol: position.symbol,
                    direction: position.direction,
                    quantity: position.quantity
                });

                // Execute close trade with correct direction
                const orderType = position.direction === 'long' ? 'Close Long' : 'Close Short';
                const closeResult = await this.tradingExecutor.executeTrade({
                    orderType,
                    quantity: position.quantity.toString(),
                    reason: reason,
                    positionId: position.id
                });

                if (closeResult.success) {
                    // Update position status
                    position.status = 'force_closed';
                    position.closeTime = new Date().toISOString();
                    position.closeReason = reason;

                    // Calculate PnL if we have current price
                    try {
                        const currentPrice = await this.marketDataService.getCurrentPrice(position.symbol);
                        position.closePrice = currentPrice.price;
                        position.pnl = this.calculatePnL(position, currentPrice.price);
                    } catch (priceError) {
                        this.logger.warning('Could not get current price for PnL calculation', {
                            positionId: position.id,
                            error: priceError.message
                        });
                    }

                    closedPositions.push({
                        id: position.id,
                        symbol: position.symbol,
                        direction: position.direction,
                        quantity: position.quantity,
                        entryPrice: position.entryPrice,
                        closePrice: position.closePrice,
                        pnl: position.pnl
                    });

                    this.logger.info('Position force closed successfully', {
                        positionId: position.id,
                        closePrice: position.closePrice,
                        pnl: position.pnl
                    });

                    // Emit event and remove from monitoring
                    this.emit('positionForceClosed', position, closeResult);
                    this.positions.delete(position.id);

                } else {
                    failedPositions.push({
                        id: position.id,
                        symbol: position.symbol,
                        direction: position.direction,
                        quantity: position.quantity,
                        error: closeResult.error
                    });

                    this.logger.error('Failed to force close position', {
                        positionId: position.id,
                        error: closeResult.error
                    });
                }

            } catch (error) {
                failedPositions.push({
                    id: position.id,
                    symbol: position.symbol,
                    direction: position.direction,
                    quantity: position.quantity,
                    error: error.message
                });

                this.logger.error('Error during force close', {
                    positionId: position.id,
                    error: error.message
                });
            }
        }

        const success = failedPositions.length === 0;

        this.logger.info('Force close operation completed', {
            totalPositions: activePositions.length,
            closedSuccessfully: closedPositions.length,
            failed: failedPositions.length,
            success
        });

        return {
            success,
            closedPositions,
            failedPositions,
            totalProcessed: activePositions.length
        };
    }

    getStatistics() {
        return {
            activePositions: this.positions.size,
            isMonitoring: this.isMonitoring,
            monitoringFrequency: this.monitoringFrequency,
            positions: this.getActivePositions().map(pos => ({
                id: pos.id,
                symbol: pos.symbol,
                entryPrice: pos.entryPrice,
                quantity: pos.quantity,
                status: pos.status,
                isTrailing: pos.isTrailing,
                tpExecutions: pos.tpExecutions.length
            }))
        };
    }
}

module.exports = PositionManager;
